<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Stripe, Mailgun, Mandrill, and others. This file provides a sane
    | default location for this type of information, allowing packages
    | to have a conventional place to find your various credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
    ],

    'ses' => [
        'key' => env('SES_KEY'),
        'secret' => env('SES_SECRET'),
        'region' => 'us-east-1',
    ],

    'sparkpost' => [
        'secret' => env('SPARKPOST_SECRET'),
    ],

    'stripe' => [
        'model' => App\User::class,
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
    ],
    'oauth_server' => [
        'client_id' => env('OAUTH_SERVER_ID'),
        'client_secret' => env('OAUTH_SERVER_SECRET'),
        'redirect' => env('OAUTH_SERVER_REDIRECT_URI'),
        'uri' => env('OAUTH_SERVER_URI'),
    ],
    'oauth' => [
        'email' => env('OAUTH_SERVER_LOGIN_EMAIL'),
        'password' => env('OAUTH_SERVER_LOGIN_PASSWORD'),
    ]

];

<?php

namespace App\ModiDB;

use App\ModiDB\ModiDBAbstract as ModiDBAbstract;
use DB;
use Session;
class Dashboard extends ModiDBAbstract {
    public $table = 'customer_call_details'; // Database table realname
    public $primaryKey = "call_id";
    public function RecentLeadCustomers($emp_customers){
        $todate = date('Y-m-d');
        $from_date = date('Y-m-d', strtotime('-7 days', strtotime($todate)));
        $emp_id = Session::get('userObject')->ref_id;
        return $results = DB::table("customer_info")
        ->join('emp_accessed_customer', function($accjoin) use ($emp_id)
            {
              $accjoin->on('emp_accessed_customer.customer_id', '=', 'customer_info.customer_id')
                    ->where('emp_accessed_customer.emp_id','=', $emp_id);
            })
        ->leftjoin('employees','employees.emp_id','=','customer_info.re_assigned_to')
        ->leftjoin('cust_email','cust_email.email_id','=','customer_info.primary_email_id')
        ->leftjoin('cust_phone','cust_phone.phone_id','=','customer_info.primary_phone_num')
        ->Leftjoin('customer_add_info','customer_add_info.customer_id', '=', 'customer_info.customer_id')
        ->leftjoin('projects','projects.project_id','=','customer_add_info.for_project')
        ->select('customer_info.customer_id','cust_phone.phone_num as primary_phone','customer_info.primary_phone_num','customer_info.primary_email_id','cust_email.email_address','employees.first_name','customer_info.customer_id as auto_number','customer_info.customer_name','customer_info.mobile_num1','customer_info.email_1','customer_info.creation_date','projects.project_name','customer_info.created_by')
        //->whereIn('customer_info.customer_id',$emp_customers)
        ->where('customer_info.lead_status','A')
        ->where('customer_info.customer_status','Lead')
        ->groupBy('customer_info.customer_id')
        ->whereBetween('customer_info.creation_date',[$from_date,$todate])->whereIn('customer_add_info.for_project',Session::get('login_project_id'));
        //->orderBy('customer_info.creation_date','DESC');
    }
	public function DashboardCallsDetails($from_date='',$creation_date='',$cust_array='') {
        $results = [];
         if(empty($cust_array)){
             return [];
         }
         $call_detaills = DB::table('customer_call_details as t1')
                        ->whereRaw("t1.customer_id in (".implode(',',$cust_array).") and t1.next_call_date = (SELECT MAX(t2.next_call_date) FROM customer_call_details t2 WHERE t2.customer_id = t1.customer_id)")
                        ->select('t1.*')
                        ->groupby('t1.customer_id')
                        ->orderBy('t1.customer_id','DESC')
                        ->get();
         //dd($call_detaills);
          /*$call_detaills = DB::select("SELECT t1.* FROM customer_call_details t1 WHERE t1.customer_id in (".implode(',',$cust_array).") and t1.next_call_date = (SELECT MAX(t2.next_call_date) FROM customer_call_details t2
                 WHERE t2.customer_id = t1.customer_id) group by t1.customer_id order by t1.customer_id DESC");*/
         return $call_detaills;
         $call_detaills = DB::table('customer_call_details as t1')
                        ->whereRaw('t1.customer_id in (".implode(',',$cust_array).") and t1.next_call_date = (SELECT MAX(t2.next_call_date) FROM customer_call_details t2 WHERE t2.customer_id = t1.customer_id)')->select('t1.*')->get();
    }
    public function getCustomerDetails($cst_id){
         $results = DB::table("customer_info")
                            ->leftjoin('employees','employees.emp_id','=','customer_info.re_assigned_to')
                            ->leftjoin('cust_phone','cust_phone.phone_id','=','customer_info.primary_phone_num')
                             ->join('customer_add_info','customer_add_info.customer_id', '=', 'customer_info.customer_id')
                            ->leftjoin('projects','projects.project_id','=','customer_add_info.for_project')
                            ->leftjoin('cust_email','cust_email.email_id','=','customer_info.primary_email_id')
                             ->where('customer_info.customer_id',$cst_id)
                             ->where('customer_info.lead_status','A')
                             ->where('customer_info.customer_status','Lead')
                             ->whereIn('customer_add_info.for_project',Session::get('login_project_id'))
                             ->select('customer_info.smart_summary','customer_add_info.for_project','customer_info.customer_id','cust_phone.phone_num as primary_phone','customer_info.primary_phone_num','customer_info.primary_email_id','cust_email.email_address','employees.first_name','customer_info.customer_id as auto_number','customer_info.customer_name','customer_info.mobile_num1','customer_info.email_1','customer_info.creation_date','projects.project_name')->first();
                             if($results){
                                $customer_name =$results->customer_name;
                                $project_name =$results->project_name;
                                $project_id =$results->for_project;
                                $first_name =$results->first_name;
                                $creation_date=$results->creation_date;
                                $smart_summary =$results->smart_summary;
                                if($results->primary_phone_num!=null){
                                $primary_phone = $results->primary_phone;
                                }
                                else{
                                    $primary_phone = $results->mobile_num1;
                                }
                             }else{
                                $customer_name=$project_name=$first_name=$primary_phone =$creation_date=$project_id=$smart_summary='' ;
                             }
            return array($customer_name,$project_name,$primary_phone,$first_name,$creation_date,$project_id,$smart_summary);
    }
    public function EmpCustomers(){
        $this->viewPageData['role_id'] = Session::get('userObject')->role_id;
        $this->viewPageData['logged_emp_id'] = Session::get('userObject')->ref_id;
        $this->viewPageData['logged_user_id'] = Session::get('userObject')->id;
        $lead_customer_obj =new CustomerInfo();
        $lead_status = 0;
        $supervisors = array();
        $emp_customers =array();
        if($this->viewPageData['role_id'] !=1){
           $lead_status = $lead_customer_obj->checkLeadAccess();
           if($lead_status>0){
            $supervisors = array_unique($lead_customer_obj->supervisedEmpTree($this->viewPageData['logged_emp_id']));
            array_push($supervisors, $this->viewPageData['logged_emp_id']);
             $emp_customers = $lead_customer_obj->AccessedCustomers($supervisors,$lead_status,0);
            }
        }else{
          $lead_status =1;
          array_push($supervisors, $this->viewPageData['logged_emp_id']);
          $emp_customers = $lead_customer_obj->AccessedCustomers($supervisors,$lead_status,0);
        }
        return $emp_customers;
  }
  
  public function callDetillsAgewise($from_date='',$today='',$emp_customers=[]) {
        /*
        SELECT e.customer_name,e.smart_summary,e.mobile_num1,g.project_name,j.first_name,h.phone_num,e.primary_phone_num,i.target_text,c.* FROM customer_call_details c
              INNER JOIN  (
              SELECT
                max(d.call_id) as max_call_id
              FROM
                customer_call_details d
              LEFT JOIN(
                SELECT MAX(call_id) AS call_id, customer_id as max_cust
                FROM
                  customer_call_details
                WHERE
                  DATE(next_call_date) >= CURDATE()
                GROUP BY
                  customer_id) d1
                ON
                  d.customer_id = d1.max_cust
              WHERE
                d.customer_id in (".implode(',',$emp_customers).") AND d1.max_cust is null AND d1.call_id IS NULL and d.customer_id is not null AND 
                next_call_date BETWEEN '$from_date' AND '$today'
                GROUP BY d.customer_id
                order by d.next_call_date DESC) p
                ON c.call_id = p.max_call_id
                  LEFT JOIN customer_info e ON e.customer_id = c.customer_id
                  LEFT JOIN customer_add_info f ON e.customer_id = f.customer_id
                  LEFT JOIN projects g ON g.project_id = f.for_project
                  LEFT JOIN cust_phone h ON h.phone_id = e.primary_phone_num
                  LEFT JOIN targets i ON i.target_text_id = c.target
                  LEFT JOIN employees j ON j.emp_id = e.re_assigned_to
                  where e.lead_status='A' and e.customer_status='Lead'
        */
                  $emp_id = Session::get('userObject')->ref_id;
       $table_data = DB::table('customer_call_details as c')
                        ->Join(
                            DB::raw("(SELECT
                                  max(d.call_id) as max_call_id
                                FROM
                                  customer_call_details d
                                LEFT JOIN(
                                  SELECT MAX(call_id) AS maxcall_id, customer_id as max_cust
                                  FROM
                                    customer_call_details
                                  WHERE
                                    DATE(next_call_date) >= CURDATE()
                                  GROUP BY
                                    customer_id) d1
                                  ON
                                    d.customer_id = d1.max_cust
                                WHERE
                                  d1.max_cust is null AND d1.maxcall_id IS NULL and d.customer_id is not null AND 
                                  next_call_date BETWEEN '$from_date' AND '$today'
                                  GROUP BY d.customer_id
                                  order by d.next_call_date DESC) p"
                                ),
                            'c.call_id','=','p.max_call_id'

                        )
                        ->join('customer_info as e', 'e.customer_id', '=', 'c.customer_id')
                        ->join('customer_add_info as f', 'e.customer_id', '=', 'f.customer_id')
                        ->leftjoin('projects as g', 'f.for_project', '=', 'g.project_id')
                        ->leftjoin('cust_phone as h','h.phone_id','=','e.primary_phone_num')
                         ->join('targets as i', 'i.target_text_id', '=', 'c.target')
                         ->leftjoin('employees as j','j.emp_id','=','e.re_assigned_to')
                         ->join('emp_accessed_customer', function($accjoin) use ($emp_id)
                          {
                            $accjoin->on('emp_accessed_customer.customer_id', '=', 'e.customer_id')
                                  ->where('emp_accessed_customer.emp_id','=', $emp_id);
                          })
                         //->whereIn('c.customer_id',$emp_customers)
                         //->whereNull('d1.call_id')
                         ->where('e.lead_status','A')
                        ->where('e.customer_status','Lead')
                        ->whereIn('f.for_project',Session::get('login_project_id'))
                        //->whereBetween('d.next_call_date',[$from_date,$today])
                        ->groupBy('e.customer_id')
                        ->select('e.customer_name','e.smart_summary','e.mobile_num1','g.project_name','j.first_name','h.phone_num','e.primary_phone_num','i.target_text','c.*')
                        ;
                        $sql = $table_data->toSql();

                        dd($from_date,$today,$emp_id,implode(',',Session::get('login_project_id')),$sql);
        return $table_data;
    }
   public function callDetillsAgeGreaterSeven($from_date='',$today='',$emp_customers=[]) {
        /*
        SELECT e.customer_name,e.smart_summary,e.mobile_num1,g.project_name,j.first_name,h.phone_num,e.primary_phone_num,i.target_text,c.* FROM customer_call_details c
              INNER JOIN  (
              SELECT
                max(d.call_id) as max_call_id
              FROM
                customer_call_details d
              LEFT JOIN(
                SELECT MAX(call_id) AS call_id, customer_id as max_cust
                FROM
                  customer_call_details
                WHERE
                  DATE(next_call_date) >= CURDATE()
                GROUP BY
                  customer_id) d1
                ON
                  d.customer_id = d1.max_cust
              WHERE
                d.customer_id in (".implode(',',$emp_customers).") AND d1.max_cust is null AND d1.call_id IS NULL and d.customer_id is not null AND 
                next_call_date BETWEEN '$from_date' AND '$today'
                GROUP BY d.customer_id
                order by d.next_call_date DESC) p
                ON c.call_id = p.max_call_id
                  LEFT JOIN customer_info e ON e.customer_id = c.customer_id
                  LEFT JOIN customer_add_info f ON e.customer_id = f.customer_id
                  LEFT JOIN projects g ON g.project_id = f.for_project
                  LEFT JOIN cust_phone h ON h.phone_id = e.primary_phone_num
                  LEFT JOIN targets i ON i.target_text_id = c.target
                  LEFT JOIN employees j ON j.emp_id = e.re_assigned_to
                  where e.lead_status='A' and e.customer_status='Lead'
        */
        $emp_id = Session::get('userObject')->ref_id;
       $table_data = DB::table('customer_call_details as c')
                        ->Join(
                            DB::raw("(SELECT
                                  max(d.call_id) as max_call_id
                                FROM
                                  customer_call_details d
                                LEFT JOIN(
                                  SELECT MAX(call_id) AS maxcall_id, customer_id as max_cust
                                  FROM
                                    customer_call_details
                                  WHERE
                                    DATE(next_call_date) >= '$today'
                                  GROUP BY
                                    customer_id) d1
                                  ON
                                    d.customer_id = d1.max_cust
                                WHERE
                                  d1.max_cust is null AND d1.maxcall_id IS NULL and d.customer_id is not null AND 
                                  next_call_date BETWEEN '$from_date' AND '$today'
                                  GROUP BY d.customer_id
                                  order by d.next_call_date DESC) p"
                                ),
                            'c.call_id','=','p.max_call_id'

                        )
                        ->join('customer_info as e', 'e.customer_id', '=', 'c.customer_id')
                        ->join('customer_add_info as f', 'e.customer_id', '=', 'f.customer_id')
                        ->leftjoin('projects as g', 'f.for_project', '=', 'g.project_id')
                        ->leftjoin('cust_phone as h','h.phone_id','=','e.primary_phone_num')
                         ->join('targets as i', 'i.target_text_id', '=', 'c.target')
                         ->leftjoin('employees as j','j.emp_id','=','e.re_assigned_to')
                         ->join('emp_accessed_customer', function($accjoin) use ($emp_id)
                          {
                            $accjoin->on('emp_accessed_customer.customer_id', '=', 'e.customer_id')
                                  ->where('emp_accessed_customer.emp_id','=', $emp_id);
                          })
                         //->whereIn('c.customer_id',$emp_customers)
                         //->whereNull('d1.call_id')
                         ->where('e.lead_status','A')
                        ->where('e.customer_status','Lead')
                        ->whereIn('f.for_project',Session::get('login_project_id'))
                        //->whereBetween('d.next_call_date',[$from_date,$today])
                         ->groupBy('e.customer_id')
                        ->select('e.customer_name','e.smart_summary','e.mobile_num1','g.project_name','j.first_name','h.phone_num','e.primary_phone_num','i.target_text','c.*')
                        ;
                        //dd($from_date,$today,$table_data->get());
        return $table_data;
    }
  public function callDetillsPostSeven($from_date='',$today='',$emp_customers=[]) {
        /*
           SELECT e.customer_name,e.smart_summary,e.mobile_num1,g.project_name,j.first_name,h.phone_num,e.primary_phone_num,i.target_text,c.*
            FROM customer_call_details c JOIN
                 (SELECT d.call_id, d.customer_id, MAX(concat(d.next_call_date, ' ',d.next_call_time)) as maxd
                  FROM customer_call_details d
                  WHERE concat(d.next_call_date, ' ',d.next_call_time) > CURDATE()
                  GROUP BY d.customer_id
                 ) cc
                 ON cc.customer_id = c.customer_id and cc.maxd = concat(c.next_call_date, ' ',c.next_call_time)
                 LEFT JOIN customer_info e ON e.customer_id = c.customer_id
                 LEFT JOIN customer_add_info f ON e.customer_id = f.customer_id
                              LEFT JOIN projects g ON g.project_id = f.for_project
                              LEFT JOIN cust_phone h ON h.phone_id = e.primary_phone_num
                              LEFT JOIN targets i ON i.target_text_id = c.target
                              LEFT JOIN employees j ON j.emp_id = e.re_assigned_to
            WHERE c.customer_id IN (4091,4092) and e.lead_status='A' and e.customer_status='Lead'
        */
        $emp_id = Session::get('userObject')->ref_id;
       $table_data = DB::table('customer_call_details as c')
                        ->Join(
                            DB::raw("(SELECT d.call_id, d.customer_id, MAX(concat(d.next_call_date, ' ',d.next_call_time)) as maxd
                                FROM customer_call_details d
                                WHERE concat(d.next_call_date, ' ',d.next_call_time) > CURDATE()
                                GROUP BY d.customer_id) cc"
                                ),
                            'cc.customer_id','=','c.customer_id'

                        )
                        ->join('customer_info as e', 'e.customer_id', '=', 'c.customer_id')
                        ->join('customer_add_info as f', 'e.customer_id', '=', 'f.customer_id')
                        ->leftjoin('projects as g', 'f.for_project', '=', 'g.project_id')
                        ->leftjoin('cust_phone as h','h.phone_id','=','e.primary_phone_num')
                         ->join('targets as i', 'i.target_text_id', '=', 'c.target')
                         ->leftjoin('employees as j','j.emp_id','=','e.re_assigned_to')
                         ->join('emp_accessed_customer', function($accjoin) use ($emp_id)
                          {
                            $accjoin->on('emp_accessed_customer.customer_id', '=', 'e.customer_id')
                                  ->where('emp_accessed_customer.emp_id','=', $emp_id);
                          })
                         //->whereIn('c.customer_id',$emp_customers)
                         ->whereRaw("cc.maxd = concat(c.next_call_date, ' ',c.next_call_time)")
                         //->whereNull('d1.call_id')
                         ->where('e.lead_status','A')
                        ->where('e.customer_status','Lead')
                        ->whereBetween('c.next_call_date',[$from_date,$today])
                        ->whereIn('f.for_project',Session::get('login_project_id'))
                        ->groupBy('e.customer_id')
                        ->select('e.customer_name','e.smart_summary','e.mobile_num1','g.project_name','j.first_name','h.phone_num','e.primary_phone_num','i.target_text','c.*')
                        ;
                        //dd($from_date,$today,$table_data->get());
        return $table_data;
    }

    public function todaycallDetails($emp_customers=[]) {
        /*
           Select d.call_id from customer_call_details d 
            LEFT JOIN  (
            SELECT max(call_id) as call_id, customer_id from customer_call_details where  date(next_call_date) > CURDATE() GROUP by customer_id ) d1 ON d.customer_id=d1.customer_id where d.customer_id=3062 and d1.call_id IS NULL and d.next_call_date = curdate()

        */
      $emp_id = Session::get('userObject')->ref_id;
       $table_data = DB::table('customer_call_details as d')
                        ->Leftjoin(
                            DB::raw("
                                    (SELECT max(call_id) as call_id , customer_id
                                            from 
                                            customer_call_details 
                                            where date(next_call_date) > CURDATE()
                                            GROUP by customer_id
                                    )  d1
                                "),
                            'd.customer_id','=','d1.customer_id'
                        )
                        ->join('customer_info as a', 'a.customer_id', '=', 'd.customer_id')
                        ->join('customer_add_info as b', 'a.customer_id', '=', 'b.customer_id')
                        ->leftjoin('projects as c', 'b.for_project', '=', 'c.project_id')
                        ->leftjoin('employees as g','g.emp_id','=','a.re_assigned_to')
                        ->leftjoin('cust_phone as e','e.phone_id','=','a.primary_phone_num')
                        ->leftjoin('targets as f', 'f.target_text_id', '=', 'd.target')
                        ->join('emp_accessed_customer', function($accjoin) use ($emp_id)
                          {
                            $accjoin->on('emp_accessed_customer.customer_id', '=', 'a.customer_id')
                                  ->where('emp_accessed_customer.emp_id','=', $emp_id);
                          })
                        //->whereIn('d.customer_id',$emp_customers)
                        ->where('d.next_call_date',date('Y-m-d'))
                        ->whereNull('d1.call_id')
                        ->where('a.lead_status','A')
                        ->where('a.customer_status','Lead')
                        ->whereIn('b.for_project',Session::get('login_project_id'))
                        ->groupby('d.customer_id')
                        ->select('d.*','a.customer_name','a.smart_summary','a.mobile_num1','c.project_name','g.first_name','e.phone_num','a.primary_phone_num','f.target_text');
        return $table_data;
    }

    public function auditReportsSubmitted30Days($checker) {
      $login_emp_id = session()->get("userObject")->ref_id;
      $login_role_id = session()->get("userObject")->role_id;
      if($login_role_id==1){
          $login_emp_id = null;
      }
      $today = date('Y-m-d');
      $from_date = date('Y-m-d', strtotime('-30 days', strtotime($today)));
      $results = [];
      $results = DB::table("audit_reports")
                  ->join('audit_report_types','audit_report_types.audit_report_id','=','audit_reports.report_type_id')
                  ->join('projects','projects.project_id','=','audit_reports.project_id')
                  ->Leftjoin('employees','employees.emp_id','=','audit_reports.submitted_by')
                  ->Leftjoin('employees as b','b.emp_id','=','audit_reports.assigned_to')
                  ->select('audit_reports.submitted_date','audit_reports.report_id as auto_number','audit_reports.report_description','audit_report_types.audit_frequency_type','audit_reports.report_status','audit_report_types.audit_report_name','employees.first_name','projects.project_name','audit_reports.report_date','b.first_name as assigned_to_name')
                  ->where('audit_reports.record_status','1')->where('audit_reports.report_status','submitted');
      if($login_emp_id){
        if($checker==0){
          $results = $results->where('audit_reports.assigned_to',$login_emp_id);
        }
      }
      $results = $results->whereBetween('audit_reports.submitted_date',[$from_date,$today]);
      return $results->whereIn('audit_reports.project_id',Session::get('login_project_id'));
    }
    public function auditReportsDueDays($from_date,$to_date) {
        $results = [];
        $login_emp_id = session()->get("userObject")->ref_id;
        $login_role_id = session()->get("userObject")->role_id;
        if($login_role_id==1){
            $login_emp_id = null;
        }
        $results = DB::table("audit_reports")
                    ->join('audit_report_types','audit_report_types.audit_report_id','=','audit_reports.report_type_id')
                    ->join('projects','projects.project_id','=','audit_reports.project_id')
                    ->Leftjoin('employees','employees.emp_id','=','audit_reports.submitted_by')
                    ->Leftjoin('employees as b','b.emp_id','=','audit_reports.assigned_to')
                    ->select('audit_reports.due_date','audit_reports.report_id as auto_number','audit_reports.report_description','audit_report_types.audit_frequency_type','audit_reports.report_status','audit_report_types.audit_report_name','employees.first_name','projects.project_name','audit_reports.report_date','b.first_name as assigned_to_name')
                    ->where('audit_reports.record_status','1')
                    ->whereIn('audit_reports.report_status',['pending','resubmit']);
                      //for approver we are bringing all records irrespective of assigned to
            if($login_emp_id && $this->auditCheckApprover()==0){
                        $results = $results->where('audit_reports.assigned_to',$login_emp_id);
                    }
            $results = $results->whereBetween('audit_reports.report_date',[$from_date,$to_date]);
        return $results->whereIn('audit_reports.project_id',Session::get('login_project_id'));
    }
    public function auditReportsPending14days($from_date,$today) {
        $results = [];
        $login_emp_id = session()->get("userObject")->ref_id;
        $login_role_id = session()->get("userObject")->role_id;
        if($login_role_id==1){
            $login_emp_id = null;
        }
        $results = DB::table("audit_reports")
                      ->join('audit_report_types','audit_report_types.audit_report_id','=','audit_reports.report_type_id')
                      ->join('projects','projects.project_id','=','audit_reports.project_id')
                      ->Leftjoin('employees','employees.emp_id','=','audit_reports.submitted_by')
                      ->Leftjoin('employees as b','b.emp_id','=','audit_reports.assigned_to')
                      ->select('audit_reports.due_date','audit_reports.report_id as auto_number','audit_reports.report_description','audit_report_types.audit_frequency_type','audit_reports.report_status','audit_report_types.audit_report_name','employees.first_name','projects.project_name','audit_reports.report_date','b.first_name as assigned_to_name')
                      ->where('audit_reports.record_status','1');
                      //for approver we are bringing all records irrespective of assigned to
            if($login_emp_id && $this->auditCheckApprover()==0){
                        $results = $results->where('audit_reports.assigned_to',$login_emp_id);
                    }
            $results = $results->whereBetween('audit_reports.report_date',[$from_date,$today])
                ->whereIn('audit_reports.report_status',['pending','resubmit']);
        return $results->whereIn('audit_reports.project_id',Session::get('login_project_id'));
    }
    public function auditReportsPendingGreater14days($from_date) {
        $results = [];
        $login_emp_id = session()->get("userObject")->ref_id;
        $login_role_id = session()->get("userObject")->role_id;
        if($login_role_id==1){
            $login_emp_id = null;
        }
        $results = DB::table("audit_reports")
                      ->join('audit_report_types','audit_report_types.audit_report_id','=','audit_reports.report_type_id')
                      ->join('projects','projects.project_id','=','audit_reports.project_id')
                      ->Leftjoin('employees','employees.emp_id','=','audit_reports.submitted_by')
                      ->Leftjoin('employees as b','b.emp_id','=','audit_reports.assigned_to')
                      ->select('audit_reports.due_date','audit_reports.report_id as auto_number','audit_reports.report_description','audit_report_types.audit_frequency_type','audit_reports.report_status','audit_report_types.audit_report_name','employees.first_name','projects.project_name','audit_reports.report_date','b.first_name as assigned_to_name')
                      ->where('audit_reports.record_status','1');
                      //for approver we are bringing all records irrespective of assigned to
            if($login_emp_id && $this->auditCheckApprover()==0){
                        $results = $results->where('audit_reports.assigned_to',$login_emp_id);
                    }
            $results = $results->where('audit_reports.report_date','<',$from_date)->whereIn('audit_reports.report_status',['pending','resubmit']);
        return $results->whereIn('audit_reports.project_id',Session::get('login_project_id'));
    }
    public function auditCheckApprover(){
        $url = '/dashboard/create/projectdocs/audit';
        $approver = 0;
        $master_sublevel = DB::table('master_sub_level')->where('route_path',$url)->first();
        if($master_sublevel){
            $master_sublevel_id = $master_sublevel->id;
            $sublevel = DB::table('sub_level_mapping')->where('flag','user')->where('user_id',session()->get('userObject')->id)->where('sublevel_filed_id',$master_sublevel_id)->first();
            if($sublevel){
                $approver = 1;
            }
        }
        return $approver;
    }
    public function EmpTeleCustomers(){
        $this->viewPageData['role_id'] = Session::get('userObject')->role_id;
        $this->viewPageData['logged_emp_id'] = Session::get('userObject')->ref_id;
        $this->viewPageData['logged_user_id'] = Session::get('userObject')->id;
        $lead_customer_obj =new CustomerCallDetails();
        $lead_status = 0;
        $supervisors = array();
        $emp_customers =array();
        if($this->viewPageData['role_id'] !=1){
           $lead_status = $lead_customer_obj->checkLeadAccess();
           if($lead_status>0){
            $supervisors = array_unique($lead_customer_obj->supervisedEmpTree($this->viewPageData['logged_emp_id']));
            array_push($supervisors, $this->viewPageData['logged_emp_id']);
             $emp_customers = $lead_customer_obj->AccessedTeleCustomers($supervisors,$lead_status,0);
            }
        }else{
          $lead_status =1;
          array_push($supervisors, $this->viewPageData['logged_emp_id']);
          $emp_customers = $lead_customer_obj->AccessedTeleCustomers($supervisors,$lead_status,0);
        }
        return $emp_customers;
  }
   public function todaytelecallDetails($emp_customers=[]) {
        /*
           Select d.call_id from customer_call_details d 
            LEFT JOIN  (
            SELECT max(call_id) as call_id, customer_id from customer_call_details where  date(next_call_date) > CURDATE() GROUP by customer_id ) d1 ON d.customer_id=d1.customer_id where d.customer_id=3062 and d1.call_id IS NULL and d.next_call_date = curdate()

        */
       $table_data = DB::table('customer_call_details as d')
                        ->Leftjoin(
                            DB::raw("
                                    (SELECT max(call_id) as call_id , customer_id
                                            from 
                                            customer_call_details 
                                            where date(next_call_date) > CURDATE()
                                            GROUP by customer_id
                                    )  d1
                                "),
                            'd.customer_id','=','d1.customer_id'
                        )
                        ->join('customer_info as a', 'a.customer_id', '=', 'd.customer_id')
                        ->join('customer_add_info as b', 'a.customer_id', '=', 'b.customer_id')
                        ->leftjoin('projects as c', 'b.for_project', '=', 'c.project_id')
                        ->leftjoin('employees as g','g.emp_id','=','a.tele_call_reassigned_to')
                        ->leftjoin('cust_phone as e','e.phone_id','=','a.primary_phone_num')
                         ->leftjoin('targets as f', 'f.target_text_id', '=', 'd.target')
                        ->whereIn('d.customer_id',$emp_customers)
                        ->where('d.next_call_date',date('Y-m-d'))
                        ->whereNull('d1.call_id')
                        //->where('a.lead_status','A')
                        ->where('a.customer_status','Lead')
                        ->whereIn('b.for_project',Session::get('login_project_id'))
                        ->groupby('d.customer_id')
                        ->select('d.*','a.customer_name','a.smart_summary','a.mobile_num1','c.project_name','g.first_name','e.phone_num','a.primary_phone_num','f.target_text');
        return $table_data;
    }
     public function telecallDetillsAgewise($from_date='',$today='',$emp_customers=[]) {
        /*
        SELECT e.customer_name,e.smart_summary,e.mobile_num1,g.project_name,j.first_name,h.phone_num,e.primary_phone_num,i.target_text,c.* FROM customer_call_details c
              INNER JOIN  (
              SELECT
                max(d.call_id) as max_call_id
              FROM
                customer_call_details d
              LEFT JOIN(
                SELECT MAX(call_id) AS call_id, customer_id as max_cust
                FROM
                  customer_call_details
                WHERE
                  DATE(next_call_date) >= CURDATE()
                GROUP BY
                  customer_id) d1
                ON
                  d.customer_id = d1.max_cust
              WHERE
                d.customer_id in (".implode(',',$emp_customers).") AND d1.max_cust is null AND d1.call_id IS NULL and d.customer_id is not null AND 
                next_call_date BETWEEN '$from_date' AND '$today'
                GROUP BY d.customer_id
                order by d.next_call_date DESC) p
                ON c.call_id = p.max_call_id
                  LEFT JOIN customer_info e ON e.customer_id = c.customer_id
                  LEFT JOIN customer_add_info f ON e.customer_id = f.customer_id
                  LEFT JOIN projects g ON g.project_id = f.for_project
                  LEFT JOIN cust_phone h ON h.phone_id = e.primary_phone_num
                  LEFT JOIN targets i ON i.target_text_id = c.target
                  LEFT JOIN employees j ON j.emp_id = e.re_assigned_to
                  where e.lead_status='A' and e.customer_status='Lead'
        */
       $table_data = DB::table('customer_call_details as c')
                        ->Join(
                            DB::raw("(SELECT
                                  max(d.call_id) as max_call_id
                                FROM
                                  customer_call_details d
                                LEFT JOIN(
                                  SELECT MAX(call_id) AS maxcall_id, customer_id as max_cust
                                  FROM
                                    customer_call_details
                                  WHERE
                                    DATE(next_call_date) >= CURDATE()
                                  GROUP BY
                                    customer_id) d1
                                  ON
                                    d.customer_id = d1.max_cust
                                WHERE
                                  d1.max_cust is null AND d1.maxcall_id IS NULL and d.customer_id is not null AND 
                                  next_call_date BETWEEN '$from_date' AND '$today'
                                  GROUP BY d.customer_id
                                  order by d.next_call_date DESC) p"
                                ),
                            'c.call_id','=','p.max_call_id'

                        )
                        ->join('customer_info as e', 'e.customer_id', '=', 'c.customer_id')
                        ->join('customer_add_info as f', 'e.customer_id', '=', 'f.customer_id')
                        ->leftjoin('projects as g', 'f.for_project', '=', 'g.project_id')
                        ->leftjoin('cust_phone as h','h.phone_id','=','e.primary_phone_num')
                         ->join('targets as i', 'i.target_text_id', '=', 'c.target')
                         ->leftjoin('employees as j','j.emp_id','=','e.tele_call_reassigned_to')
                         ->whereIn('c.customer_id',$emp_customers)
                         //->whereNull('d1.call_id')
                         //->where('e.lead_status','A')
                        ->where('e.customer_status','Lead')
                        ->whereIn('f.for_project',Session::get('login_project_id'))
                        //->whereBetween('d.next_call_date',[$from_date,$today])
                        ->select('e.customer_name','e.smart_summary','e.mobile_num1','g.project_name','j.first_name','h.phone_num','e.primary_phone_num','i.target_text','c.*')
                        ;
                        //dd($from_date,$today,$table_data->get());
        return $table_data;
    }
    public function telecallDetillsAgeGreaterSeven($from_date='',$today='',$emp_customers=[]) {
        /*
        SELECT e.customer_name,e.smart_summary,e.mobile_num1,g.project_name,j.first_name,h.phone_num,e.primary_phone_num,i.target_text,c.* FROM customer_call_details c
              INNER JOIN  (
              SELECT
                max(d.call_id) as max_call_id
              FROM
                customer_call_details d
              LEFT JOIN(
                SELECT MAX(call_id) AS call_id, customer_id as max_cust
                FROM
                  customer_call_details
                WHERE
                  DATE(next_call_date) >= CURDATE()
                GROUP BY
                  customer_id) d1
                ON
                  d.customer_id = d1.max_cust
              WHERE
                d.customer_id in (".implode(',',$emp_customers).") AND d1.max_cust is null AND d1.call_id IS NULL and d.customer_id is not null AND 
                next_call_date BETWEEN '$from_date' AND '$today'
                GROUP BY d.customer_id
                order by d.next_call_date DESC) p
                ON c.call_id = p.max_call_id
                  LEFT JOIN customer_info e ON e.customer_id = c.customer_id
                  LEFT JOIN customer_add_info f ON e.customer_id = f.customer_id
                  LEFT JOIN projects g ON g.project_id = f.for_project
                  LEFT JOIN cust_phone h ON h.phone_id = e.primary_phone_num
                  LEFT JOIN targets i ON i.target_text_id = c.target
                  LEFT JOIN employees j ON j.emp_id = e.re_assigned_to
                  where e.lead_status='A' and e.customer_status='Lead'
        */
       $table_data = DB::table('customer_call_details as c')
                        ->Join(
                            DB::raw("(SELECT
                                  max(d.call_id) as max_call_id
                                FROM
                                  customer_call_details d
                                LEFT JOIN(
                                  SELECT MAX(call_id) AS maxcall_id, customer_id as max_cust
                                  FROM
                                    customer_call_details
                                  WHERE
                                    DATE(next_call_date) >= '$today'
                                  GROUP BY
                                    customer_id) d1
                                  ON
                                    d.customer_id = d1.max_cust
                                WHERE
                                  d1.max_cust is null AND d1.maxcall_id IS NULL and d.customer_id is not null AND 
                                  next_call_date BETWEEN '$from_date' AND '$today'
                                  GROUP BY d.customer_id
                                  order by d.next_call_date DESC) p"
                                ),
                            'c.call_id','=','p.max_call_id'

                        )
                        ->join('customer_info as e', 'e.customer_id', '=', 'c.customer_id')
                        ->join('customer_add_info as f', 'e.customer_id', '=', 'f.customer_id')
                        ->leftjoin('projects as g', 'f.for_project', '=', 'g.project_id')
                        ->leftjoin('cust_phone as h','h.phone_id','=','e.primary_phone_num')
                         ->join('targets as i', 'i.target_text_id', '=', 'c.target')
                         ->leftjoin('employees as j','j.emp_id','=','e.tele_call_reassigned_to')
                         ->whereIn('c.customer_id',$emp_customers)
                         //->whereNull('d1.call_id')
                        // ->where('e.lead_status','A')
                        ->where('e.customer_status','Lead')
                        ->whereIn('f.for_project',Session::get('login_project_id'))
                        //->whereBetween('d.next_call_date',[$from_date,$today])
                        ->select('e.customer_name','e.smart_summary','e.mobile_num1','g.project_name','j.first_name','h.phone_num','e.primary_phone_num','i.target_text','c.*')
                        ;
                        //dd($from_date,$today,$table_data->get());
        return $table_data;
    }
    public function telecallDetillsPostSeven($from_date='',$today='',$emp_customers=[]) {
        /*
           SELECT e.customer_name,e.smart_summary,e.mobile_num1,g.project_name,j.first_name,h.phone_num,e.primary_phone_num,i.target_text,c.*
            FROM customer_call_details c JOIN
                 (SELECT d.call_id, d.customer_id, MAX(concat(d.next_call_date, ' ',d.next_call_time)) as maxd
                  FROM customer_call_details d
                  WHERE concat(d.next_call_date, ' ',d.next_call_time) > CURDATE()
                  GROUP BY d.customer_id
                 ) cc
                 ON cc.customer_id = c.customer_id and cc.maxd = concat(c.next_call_date, ' ',c.next_call_time)
                 LEFT JOIN customer_info e ON e.customer_id = c.customer_id
                 LEFT JOIN customer_add_info f ON e.customer_id = f.customer_id
                              LEFT JOIN projects g ON g.project_id = f.for_project
                              LEFT JOIN cust_phone h ON h.phone_id = e.primary_phone_num
                              LEFT JOIN targets i ON i.target_text_id = c.target
                              LEFT JOIN employees j ON j.emp_id = e.re_assigned_to
            WHERE c.customer_id IN (4091,4092) and e.lead_status='A' and e.customer_status='Lead'
        */
       $table_data = DB::table('customer_call_details as c')
                        ->Join(
                            DB::raw("(SELECT d.call_id, d.customer_id, MAX(concat(d.next_call_date, ' ',d.next_call_time)) as maxd
                                FROM customer_call_details d
                                WHERE concat(d.next_call_date, ' ',d.next_call_time) > CURDATE()
                                GROUP BY d.customer_id) cc"
                                ),
                            'cc.customer_id','=','c.customer_id'

                        )
                        ->join('customer_info as e', 'e.customer_id', '=', 'c.customer_id')
                        ->join('customer_add_info as f', 'e.customer_id', '=', 'f.customer_id')
                        ->leftjoin('projects as g', 'f.for_project', '=', 'g.project_id')
                        ->leftjoin('cust_phone as h','h.phone_id','=','e.primary_phone_num')
                         ->join('targets as i', 'i.target_text_id', '=', 'c.target')
                         ->leftjoin('employees as j','j.emp_id','=','e.tele_call_reassigned_to')
                         ->whereIn('c.customer_id',$emp_customers)
                         ->whereRaw("cc.maxd = concat(c.next_call_date, ' ',c.next_call_time)")
                         //->whereNull('d1.call_id')
                         //->where('e.lead_status','A')
                        ->where('e.customer_status','Lead')
                        ->whereBetween('c.next_call_date',[$from_date,$today])
                        ->whereIn('f.for_project',Session::get('login_project_id'))
                        ->select('e.customer_name','e.smart_summary','e.mobile_num1','g.project_name','j.first_name','h.phone_num','e.primary_phone_num','i.target_text','c.*')
                        ;
                        //dd($from_date,$today,$table_data->get());
        return $table_data;
    }
public function purchaseTableallDetailRows($requisition_num,$company_id,$project_id) {
         $results = [];
        // dd($type);
        //dd($pname,$blockno,$cname,$bdate,$flat_num,$id);
            $results = DB::table('purchase_order')
                        //->join('requisition_details', 'requisition_details.requisition_id', '=' ,'purchase_order.requisition_id')
                        // ->join('suppliers', 'suppliers.supplier_id', '=', 'purchase_order.supplier_id')
                        ->join('projects', 'projects.project_id', '=' ,'purchase_order.project_id')
                        ->join('company_master', 'company_master.company_id', '=' ,'purchase_order.company_id')
                         ->join("item_category","item_category.item_category_id","=","purchase_order.category_id")
                         ->select('purchase_order.order_num','purchase_order.submitted_for_approval','purchase_order.order_id','purchase_order.requisition_num','purchase_order.active_flag','projects.project_name','company_master.company_name','item_category.item_category_name','purchase_order.first_approval_status','purchase_order.second_approval_status','purchase_order.reject_status','purchase_order.close_order');
                        if(isset($requisition_num) && $requisition_num != ''){
                            $results = $results->where('purchase_order.requisition_num','LIKE','%'.$requisition_num.'%');
                        }
                        
                         if(isset($company_id) && $company_id != '' && $company_id != 0){
                        $results = $results->where('purchase_order.company_id',$company_id);
                        }
                        if(isset($project_id) && $project_id != '' && $project_id != 0){
                            $results = $results->where('purchase_order.project_id',$project_id);
                        }else{
                        $results = $results->whereIn('purchase_order.project_id',Session::get('login_project_id'));
                    }
                        
                    $results = $results->where('purchase_order.active_flag','1')->orderBy('purchase_order.order_id','DESC');
                        
                        
             
            return $results;
    }  
public function purchaseTableallRows($requisition_num,$company_id,$project_id) {
         $results = [];
        // dd($type);
        //dd($pname,$blockno,$cname,$bdate,$flat_num,$id);
            $results = DB::table('purchase_order')
                        //->join('requisition_details', 'requisition_details.requisition_id', '=' ,'purchase_order.requisition_id')
                        // ->join('suppliers', 'suppliers.supplier_id', '=', 'purchase_order.supplier_id')
                        ->join('projects', 'projects.project_id', '=' ,'purchase_order.project_id')
                        ->join('company_master', 'company_master.company_id', '=' ,'purchase_order.company_id')
                         ->join("item_category","item_category.item_category_id","=","purchase_order.category_id")
                         ->select('purchase_order.order_num','purchase_order.submitted_for_approval','purchase_order.order_id','purchase_order.requisition_num','purchase_order.active_flag','projects.project_name','company_master.company_name','item_category.item_category_name','purchase_order.first_approval_status','purchase_order.second_approval_status','purchase_order.reject_status','purchase_order.close_order');
                        if(isset($requisition_num) && $requisition_num != ''){
                            $results = $results->where('purchase_order.requisition_num','LIKE','%'.$requisition_num.'%');
                        }
                        
                         if(isset($company_id) && $company_id != '' && $company_id != 0){
                        $results = $results->where('purchase_order.company_id',$company_id);
                        }
                        if(isset($project_id) && $project_id != '' && $project_id != 0){
                            $results = $results->where('purchase_order.project_id',$project_id);
                        }else{
                        $results = $results->whereIn('purchase_order.project_id',Session::get('login_project_id'));
                    }
                        
                    $results = $results->where('purchase_order.submitted_for_approval','0')->where('purchase_order.close_order','0')->orderBy('purchase_order.order_id','DESC');
                        
                        
             
            return $results;
    }  
    public function purchaseTablelevel1Rows($requisition_num,$company_id,$project_id) {
         $results = [];
        // dd($type);
        //dd($pname,$blockno,$cname,$bdate,$flat_num,$id);
            $results = DB::table('purchase_order')
                        //->join('requisition_details', 'requisition_details.requisition_id', '=' ,'purchase_order.requisition_id')
                        // ->join('suppliers', 'suppliers.supplier_id', '=', 'purchase_order.supplier_id')
                        ->join('projects', 'projects.project_id', '=' ,'purchase_order.project_id')
                        ->join('company_master', 'company_master.company_id', '=' ,'purchase_order.company_id')
                         ->join("item_category","item_category.item_category_id","=","purchase_order.category_id")
                         ->select('purchase_order.order_num','purchase_order.submitted_for_approval','purchase_order.order_id','purchase_order.requisition_num','purchase_order.active_flag','projects.project_name','company_master.company_name','item_category.item_category_name','purchase_order.first_approval_status','purchase_order.second_approval_status','purchase_order.reject_status','purchase_order.close_order');
                        if(isset($requisition_num) && $requisition_num != ''){
                            $results = $results->where('purchase_order.requisition_num','LIKE','%'.$requisition_num.'%');
                        }
                        
                         if(isset($company_id) && $company_id != '' && $company_id != 0){
                        $results = $results->where('purchase_order.company_id',$company_id);
                        }
                        if(isset($project_id) && $project_id != '' && $project_id != 0){
                            $results = $results->where('purchase_order.project_id',$project_id);
                        }else{
                        $results = $results->whereIn('purchase_order.project_id',Session::get('login_project_id'));
                    }
                        
                     $results = $results->where('purchase_order.submitted_for_approval','1')->where('purchase_order.close_order','0')->where('purchase_order.first_approval_status','0')->where('purchase_order.reject_status','0')->orderBy('purchase_order.order_id','DESC');
                        
                        
             
            return $results;
    }  
    public function purchaseTablelevel2Rows($requisition_num,$company_id,$project_id) {
         $results = [];
        // dd($type);
        //dd($pname,$blockno,$cname,$bdate,$flat_num,$id);
            $results = DB::table('purchase_order')
                        //->join('requisition_details', 'requisition_details.requisition_id', '=' ,'purchase_order.requisition_id')
                        // ->join('suppliers', 'suppliers.supplier_id', '=', 'purchase_order.supplier_id')
                        ->join('projects', 'projects.project_id', '=' ,'purchase_order.project_id')
                        ->join('company_master', 'company_master.company_id', '=' ,'purchase_order.company_id')
                         ->join("item_category","item_category.item_category_id","=","purchase_order.category_id")
                         ->select('purchase_order.order_num','purchase_order.submitted_for_approval','purchase_order.order_id','purchase_order.requisition_num','purchase_order.active_flag','projects.project_name','company_master.company_name','item_category.item_category_name','purchase_order.first_approval_status','purchase_order.second_approval_status','purchase_order.reject_status','purchase_order.close_order');
                        if(isset($requisition_num) && $requisition_num != ''){
                            $results = $results->where('purchase_order.requisition_num','LIKE','%'.$requisition_num.'%');
                        }
                        
                         if(isset($company_id) && $company_id != '' && $company_id != 0){
                        $results = $results->where('purchase_order.company_id',$company_id);
                        }
                        if(isset($project_id) && $project_id != '' && $project_id != 0){
                            $results = $results->where('purchase_order.project_id',$project_id);
                        }else{
                        $results = $results->whereIn('purchase_order.project_id',Session::get('login_project_id'));
                    }
                        
                     $results = $results->where('purchase_order.submitted_for_approval','1')->where('purchase_order.first_approval_status','1')->where('purchase_order.second_approval_status','0')->where('purchase_order.close_order','0')->where('purchase_order.reject_status','0')->orderBy('purchase_order.order_id','DESC');
                        
                        
             
            return $results;
    }  
    public function purchaseTableapprovedRows($requisition_num,$company_id,$project_id) {
         $results = [];
        // dd($type);
        //dd($pname,$blockno,$cname,$bdate,$flat_num,$id);
            $results = DB::table('purchase_order')
                        //->join('requisition_details', 'requisition_details.requisition_id', '=' ,'purchase_order.requisition_id')
                        // ->join('suppliers', 'suppliers.supplier_id', '=', 'purchase_order.supplier_id')
                        ->join('projects', 'projects.project_id', '=' ,'purchase_order.project_id')
                        ->join('company_master', 'company_master.company_id', '=' ,'purchase_order.company_id')
                         ->join("item_category","item_category.item_category_id","=","purchase_order.category_id")
                         ->select('purchase_order.order_num','purchase_order.submitted_for_approval','purchase_order.order_id','purchase_order.requisition_num','purchase_order.active_flag','projects.project_name','company_master.company_name','item_category.item_category_name','purchase_order.first_approval_status','purchase_order.second_approval_status','purchase_order.reject_status','purchase_order.close_order');
                        if(isset($requisition_num) && $requisition_num != ''){
                            $results = $results->where('purchase_order.requisition_num','LIKE','%'.$requisition_num.'%');
                        }
                        
                         if(isset($company_id) && $company_id != '' && $company_id != 0){
                        $results = $results->where('purchase_order.company_id',$company_id);
                        }
                        if(isset($project_id) && $project_id != '' && $project_id != 0){
                            $results = $results->where('purchase_order.project_id',$project_id);
                        }else{
                        $results = $results->whereIn('purchase_order.project_id',Session::get('login_project_id'));
                    }
                        
                     $results = $results->where('purchase_order.submitted_for_approval','1')->where('purchase_order.first_approval_status','1')->where('purchase_order.second_approval_status','1')->where('purchase_order.close_order','0')->where('purchase_order.reject_status','0')->where('purchase_order.active_flag',1)->orderBy('purchase_order.order_id','DESC');
                        
                        
             
            return $results;
    }  
    public function purchaseTableclosedRows($requisition_num,$company_id,$project_id) {
         $results = [];
        // dd($type);
        //dd($pname,$blockno,$cname,$bdate,$flat_num,$id);
            $results = DB::table('purchase_order')
                        //->join('requisition_details', 'requisition_details.requisition_id', '=' ,'purchase_order.requisition_id')
                        // ->join('suppliers', 'suppliers.supplier_id', '=', 'purchase_order.supplier_id')
                        ->join('projects', 'projects.project_id', '=' ,'purchase_order.project_id')
                        ->join('company_master', 'company_master.company_id', '=' ,'purchase_order.company_id')
                         ->join("item_category","item_category.item_category_id","=","purchase_order.category_id")
                         ->select('purchase_order.order_num','purchase_order.submitted_for_approval','purchase_order.order_id','purchase_order.requisition_num','purchase_order.active_flag','projects.project_name','company_master.company_name','item_category.item_category_name','purchase_order.first_approval_status','purchase_order.second_approval_status','purchase_order.reject_status','purchase_order.close_order');
                        if(isset($requisition_num) && $requisition_num != ''){
                            $results = $results->where('purchase_order.requisition_num','LIKE','%'.$requisition_num.'%');
                        }
                        
                         if(isset($company_id) && $company_id != '' && $company_id != 0){
                        $results = $results->where('purchase_order.company_id',$company_id);
                        }
                        if(isset($project_id) && $project_id != '' && $project_id != 0){
                            $results = $results->where('purchase_order.project_id',$project_id);
                        }else{
                        $results = $results->whereIn('purchase_order.project_id',Session::get('login_project_id'));
                    }
                        
                    $results = $results->where('purchase_order.close_order','1')->orderBy('purchase_order.order_id','DESC');
                        
                        
             
            return $results;
    }  
    public function purchaseTablecancelledRows($requisition_num,$company_id,$project_id) {
         $results = [];
        // dd($type);
        //dd($pname,$blockno,$cname,$bdate,$flat_num,$id);
            $results = DB::table('purchase_order')
                        //->join('requisition_details', 'requisition_details.requisition_id', '=' ,'purchase_order.requisition_id')
                        // ->join('suppliers', 'suppliers.supplier_id', '=', 'purchase_order.supplier_id')
                        ->join('projects', 'projects.project_id', '=' ,'purchase_order.project_id')
                        ->join('company_master', 'company_master.company_id', '=' ,'purchase_order.company_id')
                         ->join("item_category","item_category.item_category_id","=","purchase_order.category_id")
                         ->select('purchase_order.order_num','purchase_order.submitted_for_approval','purchase_order.order_id','purchase_order.requisition_num','purchase_order.active_flag','projects.project_name','company_master.company_name','item_category.item_category_name','purchase_order.first_approval_status','purchase_order.second_approval_status','purchase_order.reject_status','purchase_order.close_order');
                        if(isset($requisition_num) && $requisition_num != ''){
                            $results = $results->where('purchase_order.requisition_num','LIKE','%'.$requisition_num.'%');
                        }
                        
                         if(isset($company_id) && $company_id != '' && $company_id != 0){
                        $results = $results->where('purchase_order.company_id',$company_id);
                        }
                        if(isset($project_id) && $project_id != '' && $project_id != 0){
                            $results = $results->where('purchase_order.project_id',$project_id);
                        }else{
                        $results = $results->whereIn('purchase_order.project_id',Session::get('login_project_id'));
                    }
                        
                    $results = $results->where('purchase_order.reject_status','1')->orderBy('purchase_order.order_id','DESC');
                        
                        
             
            return $results;
    }
}
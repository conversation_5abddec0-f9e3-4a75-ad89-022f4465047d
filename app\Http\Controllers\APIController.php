<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use Illuminate\Support\Facades\Http;
use DB;
use App\ModiDB\Projects;
use App\ModiDB\CustomerInfo;
use App\ModiDB\CustomerAdditionalInfo;
use App\ModiDB\CustAddresses;
use App\ModiDB\CustPhone;
use App\ModiDB\CustEmail;
use App\ModiDB\CustomerNameHistory;
use App\ModiDB\CustomerCallDetails;
use Session;
use DateTime;
class APIController extends Controller
{
    public function apiLogin(){
        
        // env also works ut at somepoint config needs to be cleared, else it will return null
        // $postData = array("email" => env('OAUTH_SERVER_LOGIN_EMAIL'), "password" => env('OAUTH_SERVER_LOGIN_PASSWORD'));
        $postData = [
                        "email" => config('services.oauth.email'),
                        "password" => config('services.oauth.password'),
                    ];
        $postData = json_encode($postData);
        $ApiUri = env('OAUTH_SERVER_URI').'api/auth/login';
        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => $ApiUri,
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>$postData,
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
          ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        dd($response);
        $responseArray = json_decode($response,true);
        if($responseArray['access_token']){
           $insert =  DB::table('api_user_login')->insert([
                            'apiuser_id'=>1,
                            'token'=> $responseArray['token_type'].' '.$responseArray['access_token'],
                            'created_at'=>date('Y-m-d H:i:s'),
                            'expired_at'=>$responseArray['expires_at']
                            ]);
        }
        return $responseArray;
    }
     public function redirect()
    {
        $queries = http_build_query([
            'client_id' => config('services.oauth_server.client_id'),
            'redirect_uri' => config('services.oauth_server.redirect'),
            'response_type' => 'code'
            //'scope' => 'view-posts'
        ]);
        //dd(config('services.oauth_server.uri'),$queries);
        return redirect(config('services.oauth_server.uri') . '/oauth/authorize?' . $queries);
    }

    public function callback(Request $request)
    {
        $response = Http::post(config('services.oauth_server.uri') . '/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => config('services.oauth_server.client_id'),
            'client_secret' => config('services.oauth_server.client_secret'),
            'redirect_uri' => config('services.oauth_server.redirect'),
            'code' => $request->code
        ]);

        $response = $response->json();

        $request->user()->token()->delete();

        $request->user()->token()->create([
            'access_token' => $response['access_token'],
            'expires_in' => $response['expires_in'],
            'refresh_token' => $response['refresh_token']
        ]);

        return redirect('/home');
    }
    public function refresh(Request $request)
    {
        $response = Http::post(config('services.oauth_server.uri') . '/oauth/token', [
            'grant_type' => 'refresh_token',
            'refresh_token' => $request->user()->token->refresh_token,
            'client_id' => config('services.oauth_server.client_id'),
            'client_secret' => config('services.oauth_server.client_secret'),
            'redirect_uri' => config('services.oauth_server.redirect'),
            'scope' => 'view-posts'
        ]);

        if ($response->status() !== 200) {
            $request->user()->token()->delete();

            return redirect('/home')
                ->withStatus('Authorization failed from OAuth server.');
        }

        $response = $response->json();
        $request->user()->token()->update([
            'access_token' => $response['access_token'],
            'expires_in' => $response['expires_in'],
            'refresh_token' => $response['refresh_token']
        ]);

        return redirect('/home');
    }
    public function ConsumeLeads(){
        // $postData = array("email" => env('OAUTH_SERVER_LOGIN_EMAIL'), "password" => env('OAUTH_SERVER_LOGIN_PASSWORD'));
        // dd($postData);
        $checkForToken = DB::table('api_user_login')->where('expired_at', '>=',date('Y-m-d H:i:s'))->first();
        //dd($checkForToken->token);
        if($checkForToken){
            $token = $checkForToken->token;
        }else{
            $responseArray = $this->apiLogin();
            $token = $responseArray['token_type'].' '.$responseArray['access_token'];
        }
        
        
        //open connection
        $ch = curl_init();
        $headers = array(
            'Content-type: application/json',
            'Authorization: '.$token,
        );
        $base_url = env('OAUTH_SERVER_URI');
        $url = $base_url.'api/auth/get-leads';

        //set the url, number of POST vars, POST data
        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_CONNECTTIMEOUT,0);
        curl_setopt($ch,CURLOPT_HTTPHEADER, $headers); 
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true);
        //execute post
        $result=curl_exec($ch);
       
        if(curl_error($ch))
        {
            $return_data['status']=0;
            $return_data['response_message']=curl_error($ch);
        }else{
           
            $ApiArray = json_decode($result, true);
            $apiLeads = [];
            $projectObj = new Projects();
            foreach ($ApiArray['data'] as $key => $value) {
                $project_name = $value['project'];
                $project_id = $value['project_id'];
                $projectData = $projectObj->getProjectSupervisor($project_id, $project_name);
                if($projectData){
                    $createParams=array();
                    $createParams['title'] = null;
                    $createParams['first_name'] = $value['f_name'];
                    $createParams['last_name'] = $value['l_name'];
                    $createParams['customer_name'] = $value['f_name']. ' ' . $value['l_name'];
                    $createParams['enquiry_type_id'] = 13;//Channel Partner
                    $createParams['source_type_id'] = 27;//Modi Properties Website
                    $createParams['for_project'] = $projectData->project_id;
                    $assignedto = 145; //sunil sir, if no incharge mapped
                    if($projectData->project_incharge!=null && $projectData->project_incharge!=''){
                        $assignedto = $projectData->project_incharge;
                    }
                    $channelPartner = DB::table('channel_partner')->where('channel_id')->where('cp_firm_name',$value['channel_id'])->first();
                    $channelPartnerId = null;
                    if($channelPartner){
                        $channelPartnerId = $channelPartner->channel_id;
                    }
                    $createParams['assigned_to'] = $assignedto;
                    $createParams['re_assigned_to']  = $assignedto;
                    $createParams['smart_summary'] = $value['subject'] .' - '. $value['notes'];
                    $createParams['remarks'] = "Lead inserted from mcodex API at ".date('Y-m-d H:i:s');
                    $createParams['customer_requirement'] = ' -';
                    $createParams['cst_phone'] = $value['phonefax'];
                    $createParams['cst_email'] = $value['email'];
                    $createParams['address_line'] = $value['location'];
                    $createParams['channel_partner_id'] = $channelPartnerId;
                    $createParams['alternative_number'] = $value['alternative_number'];
                    $createParams['source'] = $value['source'];
                    $this->CustomerInsert($createParams);
                    $apiLeads[]=$value['api_auto_id'];
                }
            }
            //update the api leads
            $postApiData = array("lead_ids" => $apiLeads);
            $postApiData = json_encode($postApiData);
            $update_url = $base_url.'api/auth/update-lead-status';
             $headers = array(
                            'Content-type: application/json',
                            'Authorization: '.$token,
                        );
             $curl = curl_init();
                curl_setopt_array($curl, array(
                  CURLOPT_URL => $update_url,
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS => $postApiData,
              CURLOPT_HTTPHEADER => $headers,
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            echo $response;
        }
        
    }
    public function CustomerInsert($arrayData){

        $params = $arrayData;

        $customerinfo = new CustomerInfo();
        $customerHistory = new CustomerNameHistory();
        $customer_add_info = new CustomerAdditionalInfo();
        $customeraddress = new CustAddresses();
        $customerphone = new CustPhone();
        $customeremail = new CustEmail();
        $customerCallDetails = new CustomerCallDetails();

        $customer_status = 'Lead';
        $createParams = []; //customer_info
        $CustAddresses = []; //cust_addresses
        $AddParams = []; //customer_add_info

        // customer_info
        // $createParams['project_id'] = $request->input('project_id');
        $createParams['title'] = $params['title'];
        $createParams['first_name'] = $params['first_name'];
        $createParams['last_name'] = $params['last_name'];
        $createParams['customer_name'] = $params['first_name']. ' ' . $params['last_name'];
        $createParams['company_name'] = null;
        $createParams['designation'] = null;
        $createParams['business_occupation_profession'] = null;
        $createParams['customer_status'] = 'Lead';
        $createParams['marital_status'] = null;
        
        // $createParams['mobile_num2'] = $request->mobile_num_2;
         $createParams['address_line'] = $params['address_line'];
         $createParams['mobile_num1'] = $params['cst_phone'];
         $createParams['email_1'] = $params['cst_email'];
         $createParams['work_phone_num'] = null;
         $createParams['home_phone_num'] = null;
         $createParams['alt_mobile_number'] = $params['alternative_number'];
         $createParams['other'] = null;
         $createParams['source'] = $params['source'];
         $createParams['code_mobile_num1'] = null;
         
        // $createParams['int_mobile'] = $request->country_code').'-'.$request->int_mobile;
         
        // $createParams['email_2'] = $request->email_2;
        $createParams['source_type_id'] = $params['source_type_id'];
        $createParams['channel_partner_id'] = $params['channel_partner_id'];
        $createParams['enquiry_type_id'] = $params['enquiry_type_id'];
        $createParams['lead_status'] = 'A';
        $createParams['prospect_status'] = 'A';
        $createParams['smart_summary'] = $params['smart_summary'];
        $createParams['broucher_sent'] = null;
        $createParams['visit_card_obt'] = null;
        $createParams['exec_meet_cust'] = null;
        $createParams['cust_visit_site'] = null;
        $createParams['prepared_by'] = $params['assigned_to'];
        $createParams['assigned_to'] = $params['assigned_to'];
        $createParams['re_assigned_to'] = $params['re_assigned_to'];
        $createParams['cis_no'] = '';
        $createParams['cis_date'] = date('Y-m-d');

        $createParams['remarks'] =$params['remarks'];
        
        // customer_add_info
        $AddParams['company_type'] = null;
        $AddParams['company_details'] = null;
        $AddParams['company_address'] = null;
        $AddParams['dob_customer'] = null;

        $AddParams['anniversary_date'] = null;
        $AddParams['spouse_title'] = null;
        $AddParams['spouse_first_name'] = null;
        $AddParams['spouse_last_name'] = null;
        $AddParams['spouse_full_name'] = null;
        $AddParams['mobile_spouse'] = null;
        $AddParams['dob_spouse'] = null;
        $AddParams['is_spouse_working'] = null;
        $AddParams['spouse_company_type'] = null;
        $AddParams['spouse_company'] = null;
        $AddParams['spouse_designation'] = null;
        $AddParams['purpose'] = null;
        $AddParams['investment_dtls'] = null;
        $AddParams['site_visited_with'] = null;
        $AddParams['customer_requirement'] = $params['customer_requirement'];
        $AddParams['hl_req'] =null;// $request->hl_req;//this field using in housing loan table [req_flag]
        //$AddParams['hl_amt_req'] = $request->hl_amt_req;
        $AddParams['hl_amt_req'] = null;
        $AddParams['hl_pre_sancationed_obtained'] = null;
        $AddParams['hl_ref_to_banker'] = null;
        $AddParams['hl_bank_id'] = 1;//this field is not required in add_info table
        
        $AddParams['for_project'] = $params['for_project'];//this field is required for report purpose taken from old cisform page.
        $AddParams['proj_visisted'] = null;
        $AddParams['proj_liked'] = null;
        $AddParams['proj_interested'] = null;
        $AddParams['discount_exp'] = null;
        $AddParams['discount_offered'] = null;
        $AddParams['home_town'] = null;
        $AddParams['other_info'] = null;
        $AddParams['old_cis_no'] = null;
        $AddParams['decision_makers'] = null;
        $AddParams['discount_offered'] = null;
        $AddParams['queries'] = null;
        $AddParams['special_req'] = null;
                
        //$createParams['created_by'] = Session::get('userObject')->id;
        $customer_id = $customerinfo->insert($createParams);
        //for new created values cis number is same as the customer id
        DB::table('customer_info')->where('customer_id',$customer_id)->update(['cis_no'=>$customer_id]);
                
        $custHistory = [];
        $custHistory['customer_id'] = $customer_id;
         $custHistory['customer_name'] = $params['customer_name'];
        $custHistory['user_id'] = 1;
        $custHistory['current_date_time'] = date('Y-m-d H:i:s');
        $customerHistory->insert($custHistory);

        $AddParams['customer_id'] = $customer_id;

        $customer_add_info->insert($AddParams);
        $CustPhone['phone_type'] = 'mob';
        $CustPhone['code'] = '';
        $CustPhone['phone_num'] = $params['cst_phone'];
        $CustPhone['is_default'] = 1;
        $CustPhone['customer_id'] = $customer_id;
        $customerphone->insert($CustPhone);

        $CustEmail['email_type'] = 'per';
        $CustEmail['email_address'] =$params['cst_email'];
        $CustEmail['is_default'] = 1;
        $CustEmail['customer_id'] = $customer_id;
        $customeremail->insert($CustEmail);

        // customer calldetails
        
        $createParams = [];
        $createParams['customer_id'] = $customer_id;
        $createParams['made_by'] = $params['assigned_to'];
        $createParams['call_date'] = date('Y-m-d'); 
        // $createParams['start_time'] = $request->input('add_start_time.' . $i);
        // $createParams['end_time'] = $request->input('add_end_time.' . $i);
        $createParams['type_of_call'] = 'p';
        $outcome_text_id = 0;
        $outcome_details = DB::table('outcomes')->select('outcome_text_id')->where('outcome_text','LIKE','Other')->first();
            if($outcome_details){
                $outcome_text_id = $outcome_details->outcome_text_id;
            }
        $createParams['outcome_of_call'] = $outcome_text_id;
        $createParams['outcome_of_call_other'] = 'M-Codex API : Default System Generated Record';
        $date = new DateTime(date('Y-m-d'));
        $date->modify('+1 day');
        $createParams['next_call_date'] = $date->format('Y-m-d');

        $createParams['next_call_time'] = '09:30';
        $createParams['target'] = '21';//
        $createParams['target_other'] = 'Default System Generated Record';
        $createParams['shall_remind'] = 1;
        $createParams['active_flag'] = 1;
        $customerCallDetails->insert($createParams);

        //update customer primary address phone and email
        $primary_values = $customerinfo->CustomerPrimaryValues($customer_id);
        DB::table('customer_info')->where('customer_id',$customer_id)->update(['primary_email_id'=>$primary_values[1],'primary_phone_num'=>$primary_values[2],'primary_address'=>$primary_values[0]]);
        if($customer_id!=0 && $customer_id!=null){
            return $customer_id;
        }
        else{
             return 0;
        }
               

    }
}
